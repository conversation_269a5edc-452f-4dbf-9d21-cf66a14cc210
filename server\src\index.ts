import cors from "@elysiajs/cors";
import { Elysia } from "elysia";
import strapi from "./routes/strapi";
import config from "./app/config";

const defaultConfig = config();

const app = new Elysia()
	.use(
		cors({
			origin: defaultConfig.AllowedHosts,
		}),
	)
	.get("/health", () => "Healthy")
	.group("/strapi", (app) => app.use(strapi))
	.listen(Bun.env.PORT || 3000);

console.log(
	`🦊 Elysia is running at ${app.server?.hostname}:${app.server?.port}`,
);

export type App = typeof app;
