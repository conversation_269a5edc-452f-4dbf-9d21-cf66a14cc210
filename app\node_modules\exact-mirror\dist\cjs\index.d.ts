import { TypeCompiler, type TypeCheck } from '@sinclair/typebox/compiler';
import type { TAnySchema, TModule } from '@sinclair/typebox';
export declare const mergeObjectIntersection: (schema: TAnySchema) => TAnySchema;
type MaybeArray<T> = T | T[];
export interface Instruction {
    optionals: string[];
    optionalsInArray: string[][];
    parentIsOptional: boolean;
    array: number;
    unions: TypeCheck<any>[][];
    unionKeys: Record<string, 1>;
    sanitize: MaybeArray<(v: string) => string> | undefined;
    /**
     * TypeCompiler is required when using Union
     *
     * Left as opt-in to reduce bundle size
     * many end-user doesn't use Union
     *
     * @default undefined
     */
    TypeCompiler?: typeof TypeCompiler;
    typeCompilerWanred?: boolean;
    modules?: TModule<any, any>;
    definitions: Record<string, TAnySchema>;
    recursion: number;
    /**
     * @default 8
     */
    recursionLimit: number;
    /**
     * If incorrect type is passed to Union value, should it be removed?
     *
     * If you check a value later, it's recommended to set this to `false`
     * otherwise, set this to true
     *
     * @default false
     */
    removeUnknownUnionType: boolean;
}
export declare function deepClone<T>(source: T, weak?: WeakMap<object, any>): T;
export declare const createMirror: <T extends TAnySchema>(schema: T, { TypeCompiler, modules, definitions, sanitize, recursionLimit, removeUnknownUnionType }?: Partial<Pick<Instruction, "TypeCompiler" | "definitions" | "sanitize" | "modules" | "recursionLimit" | "removeUnknownUnionType">>) => ((v: T["static"]) => T["static"]);
export default createMirror;
