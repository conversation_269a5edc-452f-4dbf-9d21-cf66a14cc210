{"name": "uint8array-extras", "version": "1.4.0", "description": "Useful utilities for working with Uint8Array (and <PERSON>uffer)", "license": "MIT", "repository": "sindresorhus/uint8array-extras", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsc index.d.ts"}, "files": ["index.js", "index.d.ts"], "keywords": ["uint8array", "uint8", "typedarray", "buffer", "typedarray", "arraybuffer", "is", "assert", "concat", "equals", "compare", "base64", "string", "atob", "btoa", "hex", "hexadecimal"], "devDependencies": {"ava": "^6.0.1", "typescript": "^5.3.3", "xo": "^0.56.0", "benchmark": "2.1.4"}}